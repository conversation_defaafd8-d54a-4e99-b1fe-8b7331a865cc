<template>
  <div class="medical-order-info-container">
    <!-- 左侧患者信息详情面板 -->
    <PatientInfoPanel :patient="patient"/>

    <!-- 右侧医嘱信息内容区域 -->
    <div class="medical-order-content-area">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container" v-loading="loading" element-loading-text="正在加载医嘱信息...">
        <div style="height: 200px;"></div>
      </div>

      <!-- 内容区域 -->
      <div v-else class="content-wrapper">
        <!-- 页面标题 -->
        <div class="page-title">
          <h2 class="title-text">医嘱信息</h2>

          <!-- 医嘱名称过滤区域 -->
          <div class="filter-section">
            <div class="filter-item">
              <label class="filter-label">医嘱名称：</label>
              <el-select
                v-model="filterOrderName"
                placeholder=""
                class="filter-select"
                clearable
                filterable
              >
                <el-option
                  v-for="option in orderNameFilterOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </div>
            <div class="filter-buttons">
              <el-button type="primary" @click="handleSearch">搜索</el-button>
              <el-button @click="handleReset">重置</el-button>
            </div>
          </div>
        </div>

        <!-- 医嘱信息表格 -->
        <div class="medical-order-table-container">
          <table class="medical-order-table">
            <!-- 表头 -->
            <thead>
            <tr class="table-header">
              <th class="header-cell">
                <span class="required-field">*</span>医嘱类型
              </th>
              <th class="header-cell">
                <span class="required-field">*</span>医嘱类别
              </th>
              <th class="header-cell">
                <span class="required-field">*</span>医嘱名称
              </th>
              <th class="header-cell">
                药品规格
              </th>
              <th class="header-cell">
                频次
              </th>
              <th class="header-cell">
                用法
              </th>
              <th class="header-cell">
                用量
              </th>
              <th class="header-cell">
                单位
              </th>
              <th class="header-cell">开始时间</th>
              <th class="header-cell">结束时间</th>
              <th class="header-cell">操作</th>
            </tr>
            </thead>
            <!-- 表体 -->
            <tbody>
            <!-- 数据行 -->
            <tr
                v-for="(item, index) in filteredMedicalOrderList"
                :key="item.orderSn || index"
                class="table-row"
            >
              <!-- 医嘱类型 -->
              <td class="table-cell">
                <el-select
                    v-if="isRowEditing(item)"
                    v-model="editForm.orderClassCode"
                    placeholder=""
                    class="cell-input"
                    :loading="medicalOrderCategoryLoading"
                    @change="handleOrderCategoryChange"
                >
                  <el-option
                      v-for="option in medicalOrderCategoryOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                  />
                </el-select>
                <span v-else>{{
                    getMedicalOrderCategoryLabel(item.orderClassCode) || item.orderClassName || item.orderClassCode
                  }}</span>
              </td>

              <!-- 医嘱类别 -->
              <td class="table-cell">
                <el-select
                    v-if="isRowEditing(item)"
                    v-model="editForm.orderType"
                    placeholder=""
                    class="cell-input"
                    :loading="medicalOrderTypeLoading"
                >
                  <el-option
                      v-for="option in medicalOrderTypeOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                  />
                </el-select>
                <span v-else>{{ getMedicalOrderTypeLabel(item.orderType) || item.orderType }}</span>
              </td>
              <!-- 医嘱名称 -->
              <td class="table-cell">
                <el-select
                    v-if="isRowEditing(item)"
                    v-model="editForm.orderItemName"
                    placeholder=""
                    class="cell-input"
                    filterable
                    remote
                    :remote-method="searchMedicalOrderNames"
                    :loading="medicalOrderNameLoading"
                    @change="handleOrderNameChange"
                    @focus="handleMedicalOrderNameFocus"
                    @visible-change="handleMedicalOrderNameVisibleChange"
                    v-load-more="loadMoreMedicalOrderNames"
                >
                  <el-option
                      v-for="option in medicalOrderNameOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                  />
                  <div v-if="medicalOrderNamePagination.loading" class="load-more-indicator">
                    正在加载...
                  </div>
                  <!-- 加载更多触发器 -->
                  <div
                    v-if="hasMoreMedicalOrderNames"
                    class="load-more-trigger"
                    @click="loadMoreMedicalOrderNames"
                  >
                    点击加载更多
                  </div>
                </el-select>
                <span v-else>{{ getMedicalOrderNameLabel(item.orderItemName) || item.orderItemName }}</span>
              </td>
              <!-- 药品规格 -->
              <td class="table-cell">
                <el-input
                    v-if="isRowEditing(item) && isDrugOrder(editForm.orderClassName)"
                    v-model="editForm.spec"
                    placeholder=""
                    class="cell-input"
                    readonly
                />
                <el-input
                    v-else-if="isRowEditing(item)"
                    v-model="editForm.spec"
                    placeholder=""
                    class="cell-input"
                />
                <span v-else>{{ item.spec || '' }}</span>
              </td>
              <!-- 频次 -->
              <td class="table-cell">
                <el-input
                    v-if="isRowEditing(item) && isDrugOrder(editForm.orderClassName)"
                    v-model="editForm.frequencyCode"
                    placeholder=""
                    class="cell-input"
                    readonly
                />
                <el-input
                    v-else-if="isRowEditing(item)"
                    v-model="editForm.frequencyCode"
                    placeholder=""
                    class="cell-input"
                />
                <span v-else>{{ item.frequencyName || item.frequencyCode || '' }}</span>
              </td>
              <!-- 用法 -->
              <td class="table-cell">
                <el-input
                    v-if="isRowEditing(item) && isDrugOrder(editForm.orderClassName)"
                    v-model="editForm.administrationRoute"
                    placeholder=""
                    class="cell-input"
                    readonly
                />
                <el-input
                    v-else-if="isRowEditing(item)"
                    v-model="editForm.administrationRoute"
                    placeholder=""
                    class="cell-input"
                />
                <span v-else>{{ item.administrationRoute || '' }}</span>
              </td>
              <!-- 用量 -->
              <td class="table-cell">
                <el-input
                    v-if="isRowEditing(item) && isDrugOrder(editForm.orderClassName)"
                    v-model="editForm.dose"
                    placeholder=""
                    class="cell-input"
                    readonly
                />
                <el-input
                    v-else-if="isRowEditing(item)"
                    v-model="editForm.dose"
                    placeholder=""
                    class="cell-input"
                />
                <span v-else>{{ item.dose || '' }}</span>
              </td>
              <!-- 单位 -->
              <td class="table-cell">
                <el-input
                    v-if="isRowEditing(item) && isDrugOrder(editForm.orderClassName)"
                    v-model="editForm.doseUnit"
                    placeholder=""
                    class="cell-input"
                    readonly
                />
                <el-input
                    v-else-if="isRowEditing(item)"
                    v-model="editForm.doseUnit"
                    placeholder=""
                    class="cell-input"
                />
                <span v-else>{{ item.doseUnit || '' }}</span>
              </td>
              <!-- 开始时间 -->
              <td class="table-cell">
                <el-date-picker
                    v-if="isRowEditing(item)"
                    v-model="editForm.orderStartDatetime"
                    type="date"
                    placeholder=""
                    class="cell-input"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                />
                <span v-else>{{ formatDateTime(item.orderStartDatetime) }}</span>
              </td>
              <!-- 结束时间 -->
              <td class="table-cell">
                <el-date-picker
                    v-if="isRowEditing(item)"
                    v-model="editForm.orderEndDatetime"
                    type="date"
                    placeholder=""
                    class="cell-input"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                />
                <span v-else>{{ formatDateTime(item.orderEndDatetime) }}</span>
              </td>
              <!-- 操作 -->
              <td class="table-cell">
                <div v-if="isRowEditing(item)" class="action-buttons">
                  <el-button
                      type="primary"
                      size="small"
                      @click="handleSave"
                      :loading="saveLoading"
                  >
                    保存
                  </el-button>
                  <el-button
                      size="small"
                      @click="handleCancelEdit"
                  >
                    取消
                  </el-button>
                </div>
                <div v-else class="action-buttons">
                  <el-button
                      type="primary"
                      size="small"
                      @click="handleEdit(index)"
                  >
                    编辑
                  </el-button>
                  <el-button
                      type="danger"
                      size="small"
                      @click="handleDelete(item)"
                      :loading="deleteLoading"
                  >
                    删除
                  </el-button>
                </div>
              </td>
            </tr>
            <!-- 编辑添加行 -->
            <tr v-if="isAdding" class="table-row add-row">
              <td class="table-cell">
                <el-select
                    v-model="addForm.orderClassCode"
                    placeholder=""
                    class="cell-input"
                    :loading="medicalOrderCategoryLoading"
                    @change="handleAddOrderCategoryChange"
                >
                  <el-option
                      v-for="option in medicalOrderCategoryOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                  />
                </el-select>
              </td>
              <td class="table-cell">
                <el-select
                    v-model="addForm.orderType"
                    placeholder=""
                    class="cell-input"
                    :loading="medicalOrderTypeLoading"
                >
                  <el-option
                      v-for="option in medicalOrderTypeOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                  />
                </el-select>
              </td>
              <td class="table-cell">
                <el-select
                    v-model="addForm.orderItemName"
                    placeholder=""
                    class="cell-input"
                    filterable
                    remote
                    :remote-method="searchMedicalOrderNames"
                    :loading="medicalOrderNameLoading"
                    @change="handleAddOrderNameChange"
                    @focus="handleMedicalOrderNameFocus"
                    @visible-change="handleMedicalOrderNameVisibleChange"
                    v-load-more="loadMoreMedicalOrderNames"
                >
                  <el-option
                      v-for="option in medicalOrderNameOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                  />
                  <div v-if="medicalOrderNamePagination.loading" class="load-more-indicator">
                    正在加载...
                  </div>
                  <!-- 加载更多触发器 -->
                  <div
                    v-if="hasMoreMedicalOrderNames"
                    class="load-more-trigger"
                    @click="loadMoreMedicalOrderNames"
                  >
                    点击加载更多
                  </div>
                </el-select>
              </td>
              <td class="table-cell">
                <el-input
                    v-if="isDrugOrder(addForm.orderClassName)"
                    v-model="addForm.spec"
                    placeholder=""
                    class="cell-input"
                    readonly
                />
                <el-input
                    v-else
                    v-model="addForm.spec"
                    placeholder=""
                    class="cell-input"
                />
              </td>
              <td class="table-cell">
                <el-input
                    v-if="isDrugOrder(addForm.orderClassName)"
                    v-model="addForm.frequencyCode"
                    placeholder=""
                    class="cell-input"
                    readonly
                />
                <el-input
                    v-else
                    v-model="addForm.frequencyCode"
                    placeholder=""
                    class="cell-input"
                />
              </td>
              <td class="table-cell">
                <el-input
                    v-if="isDrugOrder(addForm.orderClassName)"
                    v-model="addForm.administrationRoute"
                    placeholder=""
                    class="cell-input"
                    readonly
                />
                <el-input
                    v-else
                    v-model="addForm.administrationRoute"
                    placeholder=""
                    class="cell-input"
                />
              </td>
              <td class="table-cell">
                <el-input
                    v-if="isDrugOrder(addForm.orderClassName)"
                    v-model="addForm.dose"
                    placeholder=""
                    class="cell-input"
                    readonly
                />
                <el-input
                    v-else
                    v-model="addForm.dose"
                    placeholder=""
                    class="cell-input"
                />
              </td>
              <td class="table-cell">
                <el-input
                    v-if="isDrugOrder(addForm.orderClassName)"
                    v-model="addForm.doseUnit"
                    placeholder=""
                    class="cell-input"
                    readonly
                />
                <el-input
                    v-else
                    v-model="addForm.doseUnit"
                    placeholder=""
                    class="cell-input"
                />
              </td>
              <td class="table-cell">
                <el-date-picker
                    v-model="addForm.orderStartDatetime"
                    type="date"
                    placeholder=""
                    class="cell-input custome-date"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                />
              </td>
              <td class="table-cell">
                <el-date-picker
                    v-model="addForm.orderEndDatetime"
                    type="date"
                    placeholder=""
                    class="cell-input custome-date"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                />
              </td>
              <td class="table-cell">
                <div class="action-buttons">
                  <el-button
                      type="primary"
                      size="small"
                      @click="handleSaveAdd"
                      :loading="saveLoading"
                  >
                    保存
                  </el-button>
                  <el-button
                      size="small"
                      @click="handleCancelAdd"
                  >
                    取消
                  </el-button>
                </div>
              </td>
            </tr>
            </tbody>
          </table>
          <!-- 添加按钮 - 使用div居中 -->
          <div v-if="!isAdding" class="add-button-container">
            <el-button
                type="primary"
                @click="handleAdd"
                class="add-record-btn"
                :icon="Plus"
            >
              添加
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- TNM分期诊断提示弹窗 -->
    <div v-if="showTnmDialog" class="tnm-dialog-overlay" @click="handleTnmDialogCancel">
      <div class="tnm-dialog-container" @click.stop>
        <div class="tnm-dialog-content">
          <div class="tnm-dialog-title">
            <img src="@/assets/images/note.png" alt="注意" class="note-icon" />
            <p class="title-text">请注意！</p>
          </div>
          <div class="tnm-dialog-message">
            <p class="message-text">肿瘤患者抗肿瘤治疗前需明确TNM分期诊断</p>
          </div>
          <div class="tnm-dialog-actions">
            <span class="action-link" @click="handleSubmitTnmDiagnosis">提交TNM诊断</span>
            <span class="action-separator">|</span>
            <span class="action-link" @click="handleContinueSaveOrder">继续保存医嘱</span>
            <span class="action-separator">|</span>
            <span class="action-link return-link" @click="handleTnmDialogCancel">返回</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {ref, reactive, onMounted, watch, computed} from 'vue'
import {ElMessage, ElMessageBox} from 'element-plus'
import {Plus} from '@element-plus/icons-vue'
import {apiServices} from '@/api'
import {useDictionaryStore} from '@/stores/dictionaryStore'
import {useRouter} from 'vue-router'
import {getAllUrlParams} from '@/utils/urlParams'
import PatientInfoPanel from './PatientInfoPanel.vue'

// 自定义指令：v-load-more
const vLoadMore = {
  mounted(el, binding) {
    console.log('v-load-more指令已挂载到元素:', el)

    // 使用更频繁的检查来确保能找到下拉框
    const checkForDropdown = () => {
      const dropdowns = document.querySelectorAll('.el-select-dropdown:not([style*="display: none"]) .el-select-dropdown__wrap')
      console.log('找到的下拉框数量:', dropdowns.length)

      dropdowns.forEach((dropdown, index) => {
        if (!dropdown.hasAttribute('data-scroll-listener')) {
          console.log(`为第${index + 1}个下拉框添加滚动监听器`)
          dropdown.setAttribute('data-scroll-listener', 'true')
          dropdown.addEventListener('scroll', function() {
            const isBottom = this.scrollHeight - this.scrollTop <= this.clientHeight + 5
            console.log('滚动事件触发:', {
              scrollHeight: this.scrollHeight,
              scrollTop: this.scrollTop,
              clientHeight: this.clientHeight,
              isBottom,
              dropdownIndex: index
            })
            if (isBottom) {
              console.log('到达底部，触发加载更多')
              binding.value()
            }
          })
        }
      })
    }

    const observer = new MutationObserver(() => {
      checkForDropdown()
    })

    observer.observe(document.body, {
      childList: true,
      subtree: true
    })

    // 立即检查一次
    setTimeout(checkForDropdown, 100)

    el.vLoadMoreObserver = observer
  },
  unmounted(el) {
    if (el.vLoadMoreObserver) {
      el.vLoadMoreObserver.disconnect()
    }
  }
}

// 定义组件props
const props = defineProps({
  patient: {
    type: Object,
    required: true,
    default: () => ({})
  }
})

// 定义组件事件
const emit = defineEmits(['count-updated'])

// 使用路由
const router = useRouter()

// 使用字典Store
const dictionaryStore = useDictionaryStore()

// 响应式数据
const loading = ref(false)
const saveLoading = ref(false)
const deleteLoading = ref(false)

// TNM分期诊断提示弹窗相关
const showTnmDialog = ref(false)
const pendingSaveData = ref(null)
const pendingSaveType = ref('') // 'edit' 或 'add'

// 字典数据相关状态
const medicalOrderTypeOptions = ref([])
const medicalOrderCategoryOptions = ref([])
const medicalOrderNameOptions = ref([])

const medicalOrderTypeLoading = ref(false)
const medicalOrderCategoryLoading = ref(false)
const medicalOrderNameLoading = ref(false)

// 医嘱名称分页加载状态
const medicalOrderNamePagination = reactive({
  pageNum: 1,
  pageSize: 20,
  total: 0,
  loading: false,
  keyword: '',
  dictType: 'medical_order_name' // 当前字典类型
})

// 计算属性：是否还有更多医嘱名称数据
const hasMoreMedicalOrderNames = computed(() => {
  const { pageNum, pageSize, total } = medicalOrderNamePagination
  return pageNum * pageSize < total && !medicalOrderNamePagination.loading
})

// 医嘱信息列表
const medicalOrderList = ref([])

// 过滤相关数据
const filterOrderName = ref('')

// 计算属性：医嘱名称过滤选项（从表格数据中提取唯一值）
const orderNameFilterOptions = computed(() => {
  const uniqueNames = new Set()
  medicalOrderList.value.forEach(item => {
    if (item.orderItemName && item.orderItemName.trim()) {
      uniqueNames.add(item.orderItemName.trim())
    }
  })

  return Array.from(uniqueNames).map(name => ({
    value: name,
    label: name
  })).sort((a, b) => a.label.localeCompare(b.label))
})

// 计算属性：过滤后的医嘱列表
const filteredMedicalOrderList = computed(() => {
  if (!filterOrderName.value) {
    return medicalOrderList.value
  }

  return medicalOrderList.value.filter(item =>
    item.orderItemName === filterOrderName.value
  )
})

// 计算属性：判断当前行是否处于编辑状态
const isRowEditing = (item) => {
  if (editingIndex.value === -1) return false
  const editingItem = medicalOrderList.value[editingIndex.value]
  return editingItem && editingItem.orderSn === item.orderSn
}

// 编辑状态
const editingIndex = ref(-1)
const isAdding = ref(false)

// 编辑表单
const editForm = reactive({
  orderSn: '',
  visitSn: '',
  orderType: '',
  orderClassCode: '',
  orderClassName: '',
  orderItemName: '',
  spec: '',
  frequencyCode: '',
  frequencyName: '',
  administrationRoute: '',
  dose: '',
  doseUnit: '',
  orderStartDatetime: '',
  orderEndDatetime: ''
})

// 添加表单
const addForm = reactive({
  orderType: '',
  orderClassCode: '',
  orderClassName: '',
  orderItemName: '',
  spec: '',
  frequencyCode: '',
  frequencyName: '',
  administrationRoute: '',
  dose: '',
  doseUnit: '',
  orderStartDatetime: '',
  orderEndDatetime: ''
})

// ========== 字典数据加载方法 ==========

/**
 * 加载字典数据
 */
const loadDictionaries = async () => {
  try {
    // 并行加载基础字典数据（医嘱名称改为按需加载）
    await Promise.all([
      loadMedicalOrderTypes(),
      loadMedicalOrderCategories()
    ])
  } catch (error) {
    ElMessage.error('字典数据加载失败，请刷新页面重试')
  }
}

/**
 * 加载医嘱类型字典
 */
const loadMedicalOrderTypes = async () => {
  try {
    medicalOrderTypeLoading.value = true
    medicalOrderTypeOptions.value = await dictionaryStore.getMedicalOrderTypes()
  } catch (error) {
    medicalOrderTypeOptions.value = []
  } finally {
    medicalOrderTypeLoading.value = false
  }
}

/**
 * 加载医嘱类别字典
 */
const loadMedicalOrderCategories = async () => {
  try {
    medicalOrderCategoryLoading.value = true
    medicalOrderCategoryOptions.value = await dictionaryStore.getMedicalOrderCategories()
  } catch (error) {
    medicalOrderCategoryOptions.value = []
  } finally {
    medicalOrderCategoryLoading.value = false
  }
}

/**
 * 加载医嘱名称字典（分页模式）
 * @param {string} orderTypeName - 医嘱类型名称，用于获取对应的医嘱名称
 * @param {boolean} isLoadMore - 是否为加载更多
 */
const loadMedicalOrderNames = async (orderTypeName = null, isLoadMore = false) => {
  if (medicalOrderNamePagination.loading) {
    console.log('医嘱名称正在加载中，跳过本次请求')
    return
  }

  try {
    medicalOrderNamePagination.loading = true

    // 设置字典类型
    const dictType = orderTypeName || 'medical_order_name'
    medicalOrderNamePagination.dictType = dictType

    const params = {
      pageNum: medicalOrderNamePagination.pageNum,
      pageSize: medicalOrderNamePagination.pageSize,
      label: medicalOrderNamePagination.keyword,
      dictType: dictType
    }

    console.log('加载医嘱名称字典:', { orderTypeName, isLoadMore, params })

    const data = await apiServices.dictionary.getMedicalOrderNamesByPage(params)
    console.log('医嘱名称字典加载结果:', data)

    // API层已经处理了数据映射，直接使用records
    const newOptions = data.records || []

    if (isLoadMore) {
      medicalOrderNameOptions.value.push(...newOptions)
    } else {
      medicalOrderNameOptions.value = newOptions
    }

    medicalOrderNamePagination.total = data.total
  } catch (error) {
    if (!isLoadMore) {
      medicalOrderNameOptions.value = []
    }
  } finally {
    medicalOrderNamePagination.loading = false
  }
}

/**
 * 加载更多医嘱名称
 */
const loadMoreMedicalOrderNames = () => {
  const { pageNum, pageSize, total, dictType } = medicalOrderNamePagination
  console.log('触发加载更多医嘱名称:', { pageNum, pageSize, total, dictType })

  if (medicalOrderNamePagination.loading) {
    console.log('正在加载中，跳过本次请求')
    return
  }

  if (pageNum * pageSize >= total) {
    console.log('已加载所有数据')
    return
  }

  console.log('开始加载下一页')
  medicalOrderNamePagination.pageNum++

  // 根据dictType确定传递给loadMedicalOrderNames的参数
  const orderTypeName = dictType === 'medical_order_name' ? null : dictType
  console.log('加载更多 - orderTypeName:', orderTypeName)

  loadMedicalOrderNames(orderTypeName, true)
}

/**
 * 搜索医嘱名称
 */
const searchMedicalOrderNames = async (query) => {
  console.log('搜索医嘱名称:', query)
  medicalOrderNamePagination.keyword = query
  medicalOrderNamePagination.pageNum = 1
  medicalOrderNameLoading.value = true
  try {
    const orderTypeName = medicalOrderNamePagination.dictType === 'medical_order_name' ? null : medicalOrderNamePagination.dictType
    console.log('搜索 - orderTypeName:', orderTypeName)
    await loadMedicalOrderNames(orderTypeName)
  } catch (error) {
    console.error('搜索医嘱名称失败:', error)
    ElMessage.error('搜索医嘱名称失败')
  } finally {
    medicalOrderNameLoading.value = false
  }
}

/**
 * 医嘱名称选择框聚焦时触发
 */
const handleMedicalOrderNameFocus = () => {
  if (medicalOrderNameOptions.value.length === 0) {
    searchMedicalOrderNames('')
  }
}

/**
 * 医嘱名称下拉框显示/隐藏时触发
 */
const handleMedicalOrderNameVisibleChange = (visible) => {
  console.log('医嘱名称下拉框显示状态变化:', visible)
  if (visible) {
    // 下拉框显示时，延迟添加滚动监听
    setTimeout(() => {
      const dropdown = document.querySelector('.el-select-dropdown:not([style*="display: none"]) .el-select-dropdown__wrap')
      if (dropdown && !dropdown.hasAttribute('data-medical-order-scroll-listener')) {
        console.log('为医嘱名称下拉框添加滚动监听器')
        dropdown.setAttribute('data-medical-order-scroll-listener', 'true')
        dropdown.addEventListener('scroll', function() {
          const isBottom = this.scrollHeight - this.scrollTop <= this.clientHeight + 5
          console.log('医嘱名称下拉框滚动:', {
            scrollHeight: this.scrollHeight,
            scrollTop: this.scrollTop,
            clientHeight: this.clientHeight,
            isBottom
          })
          if (isBottom) {
            console.log('医嘱名称下拉框到达底部，触发加载更多')
            loadMoreMedicalOrderNames()
          }
        })
      }
    }, 100)
  }
}

// ========== 数据加载方法 ==========

/**
 * 加载医嘱信息列表
 */
const loadMedicalOrderList = async () => {
  if (!props.patient?.visitSn) {
    return
  }

  try {
    loading.value = true

    const data = await apiServices.medicalOrder.getList(props.patient.visitSn)
    medicalOrderList.value = data || []

  } catch (error) {
    ElMessage.error('加载医嘱信息列表失败: ' + error.message)
    medicalOrderList.value = []
  } finally {
    loading.value = false
  }
}

// ========== 工具方法 ==========

/**
 * 获取医嘱类型标签
 */
const getMedicalOrderTypeLabel = (value) => {
  const option = medicalOrderTypeOptions.value.find(item => item.value === value)
  return option ? option.label : value
}

/**
 * 获取医嘱类别标签
 */
const getMedicalOrderCategoryLabel = (value) => {
  const option = medicalOrderCategoryOptions.value.find(item => item.value === value)
  return option ? option.label : value
}

/**
 * 获取医嘱名称标签
 */
const getMedicalOrderNameLabel = (value) => {
  const option = medicalOrderNameOptions.value.find(item => item.value === value)
  return option ? option.label : value
}



/**
 * 格式化日期时间
 */
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return dateTime.replace('T', ' ').substring(0, 10)
}

/**
 * 判断是否为药品类医嘱
 * @param {string} orderClassCode - 医嘱类型代码
 * @returns {boolean} 是否为药品类医嘱
 */
const isDrugOrder = (orderClassCode) => {
  if (!orderClassCode) return false
  // 常见的药品类医嘱类型代码（可根据实际业务调整）
  const drugOrderTypes = ['药品', '西药', '中药', '中成药', '药物', 'DRUG', 'MEDICINE']
  return drugOrderTypes.some(type =>
    orderClassCode.includes(type) ||
    orderClassCode.toLowerCase().includes(type.toLowerCase())
  )
}

/**
 * 解析药品类医嘱的remark字段
 * @param {string} remark - 备注字段内容
 * @returns {Object} 解析结果
 */
const parseRemarkField = (remark) => {
  const result = {
    spec: '',           // 规格
    frequency: '',      // 频次
    usage: '',          // 用法
    dosage: '',         // 用量
    unit: '',           // 单位
    success: false      // 解析是否成功
  }

  if (!remark || typeof remark !== 'string') {
    return result
  }

  try {
    // 使用 "##" 作为分隔符进行解析
    const parts = remark.split('##').map(part => part.trim())

    // 按顺序提取信息：规格##频次##用法##用量##单位
    if (parts.length >= 5) {
      result.spec = parts[0] || ''
      result.frequency = parts[1] || ''
      result.usage = parts[2] || ''
      result.dosage = parts[3] || ''
      result.unit = parts[4] || ''
      result.success = true
    } else if (parts.length > 0) {
      // 部分解析，尽可能提取可用信息
      result.spec = parts[0] || ''
      if (parts.length > 1) result.frequency = parts[1] || ''
      if (parts.length > 2) result.usage = parts[2] || ''
      if (parts.length > 3) result.dosage = parts[3] || ''
      if (parts.length > 4) result.unit = parts[4] || ''
      result.success = parts.length > 1 // 至少有两个字段才算部分成功
    }
  } catch (error) {
    console.error('解析remark字段失败:', error)
  }

  return result
}

/**
 * 从医嘱名称数据中提取药品信息
 * @param {Object} orderNameData - 医嘱名称数据
 * @param {Object} targetForm - 目标表单对象
 */
const extractDrugInfoFromOrderName = (orderNameData, targetForm) => {
  try {
    console.log('从医嘱名称数据中提取药品信息:', orderNameData)

    // 方式1：如果医嘱名称数据中直接包含药品信息字段
    if (orderNameData.spec) targetForm.spec = orderNameData.spec
    if (orderNameData.frequency) {
      targetForm.frequencyCode = orderNameData.frequency
      targetForm.frequencyName = orderNameData.frequency
    }
    if (orderNameData.usage) targetForm.administrationRoute = orderNameData.usage
    if (orderNameData.dosage) targetForm.dose = orderNameData.dosage
    if (orderNameData.unit) targetForm.doseUnit = orderNameData.unit

    // 方式2：如果医嘱名称数据中有remark字段，解析remark但不填充到表单的备注字段
    if (orderNameData.remark) {
      const parsedData = parseRemarkField(orderNameData.remark)
      if (parsedData.success) {
        autoFillDrugInfo(parsedData, targetForm)
        return
      }
    }

    // 方式3：如果医嘱名称数据中有description字段，尝试解析
    if (orderNameData.description && orderNameData.description.includes('##')) {
      const parsedData = parseRemarkField(orderNameData.description)
      if (parsedData.success) {
        autoFillDrugInfo(parsedData, targetForm)
        return
      }
    }

    console.log('药品信息提取完成')
  } catch (error) {
    console.error('从医嘱名称数据中提取药品信息失败:', error)
  }
}

/**
 * 自动填充药品信息到表单
 * @param {Object} parsedData - 解析后的数据
 * @param {Object} targetForm - 目标表单对象
 */
const autoFillDrugInfo = (parsedData, targetForm) => {
  if (!parsedData.success) {
    return
  }

  try {
    // 直接填充解析出的文本值，不再匹配字典
    if (parsedData.spec) {
      targetForm.spec = parsedData.spec
    }

    if (parsedData.frequency) {
      targetForm.frequencyCode = parsedData.frequency
      targetForm.frequencyName = parsedData.frequency
    }

    if (parsedData.usage) {
      targetForm.administrationRoute = parsedData.usage
    }

    if (parsedData.dosage) {
      targetForm.dose = parsedData.dosage
    }

    if (parsedData.unit) {
      targetForm.doseUnit = parsedData.unit
    }

    console.log('药品信息自动填充完成:', parsedData)
  } catch (error) {
    console.error('自动填充药品信息失败:', error)
  }
}

/**
 * 清空编辑表单
 */
const clearEditForm = () => {
  Object.assign(editForm, {
    orderSn: '',
    visitSn: '',
    orderType: '',
    orderClassCode: '',
    orderClassName: '',
    orderItemName: '',
    spec: '',
    frequencyCode: '',
    frequencyName: '',
    administrationRoute: '',
    dose: '',
    doseUnit: '',
    orderStartDatetime: '',
    orderEndDatetime: ''
  })
}

/**
 * 清空添加表单
 */
const clearAddForm = () => {
  Object.assign(addForm, {
    orderType: '',
    orderClassCode: '',
    orderClassName: '',
    orderItemName: '',
    spec: '',
    frequencyCode: '',
    frequencyName: '',
    administrationRoute: '',
    dose: '',
    doseUnit: '',
    orderStartDatetime: '',
    orderEndDatetime: ''
  })
}

// ========== 事件处理方法 ==========

/**
 * 处理医嘱类别选择变化
 */
const handleOrderCategoryChange = async (value) => {
  const selectedOption = medicalOrderCategoryOptions.value.find(item => item.value === value)
  if (selectedOption) {
    editForm.orderClassName = selectedOption.label

    // 重置分页状态
    medicalOrderNamePagination.pageNum = 1
    medicalOrderNamePagination.keyword = ''
    medicalOrderNamePagination.dictType = selectedOption.label

    // 根据医嘱类型名称动态加载对应的医嘱名称字典数据
    await loadMedicalOrderNames(selectedOption.label)
  }

  // 清空医嘱名称选择，因为类型变化了
  editForm.orderItemName = ''

  // 如果是药品类医嘱，清空药品相关字段
  if (isDrugOrder(selectedOption.label)) {
    editForm.spec = ''
    editForm.frequencyCode = ''
    editForm.frequencyName = ''
    editForm.administrationRoute = ''
    editForm.dose = ''
    editForm.doseUnit = ''
  }
}

/**
 * 处理医嘱名称选择变化
 */
const handleOrderNameChange = (value) => {
  console.log('医嘱名称选择变化:', value)
  console.log('当前医嘱类型:', editForm.orderClassName)
  console.log('是否为药品类医嘱:', isDrugOrder(editForm.orderClassName))

  // 如果是药品类医嘱，从医嘱名称数据中提取药品信息
  if (isDrugOrder(editForm.orderClassName)) {
    const selectedOrderName = medicalOrderNameOptions.value.find(item => item.value === value)
    console.log('选中的医嘱名称数据:', selectedOrderName)
    console.log('所有医嘱名称选项:', medicalOrderNameOptions.value)

    if (selectedOrderName) {
      // 从医嘱名称数据中提取药品信息
      extractDrugInfoFromOrderName(selectedOrderName, editForm)
    } else {
      console.warn('未找到对应的医嘱名称数据')
    }
  }
}



/**
 * 处理编辑
 */
const handleEdit = async (index) => {
  // 如果有正在编辑的行，先保存
  if (editingIndex.value !== -1) {
    await handleSave()
  }

  // 如果有正在添加的行，取消添加
  if (isAdding.value) {
    handleCancelAdd()
  }

  // 从过滤后的列表中获取项目
  const item = filteredMedicalOrderList.value[index]
  // 在原始列表中找到对应的索引
  const originalIndex = medicalOrderList.value.findIndex(originalItem =>
    originalItem.orderSn === item.orderSn
  )
  editingIndex.value = originalIndex

  // 填充编辑表单
  Object.assign(editForm, {
    orderSn: item.orderSn || '',
    visitSn: item.visitSn || '',
    orderType: item.orderType || '',
    orderClassCode: item.orderClassCode || '',
    orderClassName: item.orderClassName || '',
    orderItemName: item.orderItemName || '',
    spec: item.spec || '',
    frequencyCode: item.frequencyCode || '',
    frequencyName: item.frequencyName || '',
    administrationRoute: item.administrationRoute || '',
    dose: item.dose || '',
    doseUnit: item.doseUnit || '',
    orderStartDatetime: item.orderStartDatetime || '',
    orderEndDatetime: item.orderEndDatetime || ''
  })

  // 如果是药品类医嘱且有remark字段，自动解析并填充
  if (isDrugOrder(item.orderClassName) && item.remark) {
    const parsedData = parseRemarkField(item.remark)
    if (parsedData.success) {
      // 延迟执行自动填充，确保表单数据已设置
      setTimeout(() => {
        autoFillDrugInfo(parsedData, editForm)
      }, 100)
    }
  }
}

/**
 * 处理取消编辑
 */
const handleCancelEdit = () => {
  editingIndex.value = -1
  clearEditForm()
}

/**
 * 处理保存
 */
const handleSave = async () => {
  if (!props.patient?.visitSn) {
    ElMessage.error('缺少患者信息，无法保存')
    return
  }

  // 验证必填字段
  if (!editForm.orderType || !editForm.orderClassCode || !editForm.orderItemName ) {
    ElMessage.error('请填写所有必填字段')
    return
  }

  // 构建保存数据（移到try块外面，这样catch块也能访问）
  const saveData = {
    orderSn: editForm.orderSn,
    visitSn: props.patient.visitSn,
    orderType: editForm.orderType,
    orderClassCode: editForm.orderClassCode,
    orderClassName: editForm.orderClassName,
    orderItemName: editForm.orderItemName,
    spec: editForm.spec || '',
    frequencyCode: editForm.frequencyCode || '',
    frequencyName: editForm.frequencyName || '',
    administrationRoute: editForm.administrationRoute || '',
    dose: editForm.dose || '',
    doseUnit: editForm.doseUnit || '',
    orderStartDatetime: editForm.orderStartDatetime || '',
    orderEndDatetime: editForm.orderEndDatetime || ''
  }

  try {
    saveLoading.value = true

    await apiServices.medicalOrder.update(editForm.orderSn, saveData)

    ElMessage.success('保存成功')

    // 重新加载列表
    await loadMedicalOrderList()

    // 退出编辑模式
    editingIndex.value = -1
    clearEditForm()

    // 触发数量更新事件
    emit('count-updated')
  } catch (error) {
    // 添加调试信息
    console.log('医嘱保存错误详情:', error)
    console.log('错误code:', error.code)
    console.log('错误message:', error.message)
    console.log('错误data:', error.data)

    // 检查是否是TNM分期诊断相关的错误（code为1002）
    if (error.code === 1002) {
      console.log('检测到code为1002，显示TNM分期诊断弹窗')
      // 保存当前数据，显示TNM分期诊断提示弹窗
      pendingSaveData.value = {
        orderSn: editForm.orderSn,
        saveData: saveData
      }
      pendingSaveType.value = 'edit'
      showTnmDialog.value = true
    } else {
      ElMessage.error('保存失败: ' + error.message)
    }
  } finally {
    saveLoading.value = false
  }
}

/**
 * 处理删除
 */
const handleDelete = async (item) => {
  if (!item.orderSn) {
    ElMessage.error('缺少必要的删除参数')
    return
  }

  try {
    await ElMessageBox.confirm(
        '确定要删除这条医嘱信息吗？删除后无法恢复。',
        '确认删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
    )

    deleteLoading.value = true


    await apiServices.medicalOrder.delete(item.orderSn)

    ElMessage.success('删除成功')

    // 重新加载列表
    await loadMedicalOrderList()

    // 触发数量更新事件
    emit('count-updated')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败: ' + error.message)
    }
  } finally {
    deleteLoading.value = false
  }
}

/**
 * 处理添加
 */
const handleAdd = () => {
  // 如果有正在编辑的行，先保存
  if (editingIndex.value !== -1) {
    handleSave()
  }

  isAdding.value = true
  clearAddForm()
}

/**
 * 处理取消添加
 */
const handleCancelAdd = () => {
  isAdding.value = false
  clearAddForm()
}

/**
 * 处理添加模式的医嘱类别选择变化
 */
const handleAddOrderCategoryChange = async (value) => {
  const selectedOption = medicalOrderCategoryOptions.value.find(item => item.value === value)
  if (selectedOption) {
    addForm.orderClassName = selectedOption.label

    // 重置分页状态
    medicalOrderNamePagination.pageNum = 1
    medicalOrderNamePagination.keyword = ''
    medicalOrderNamePagination.dictType = selectedOption.label

    // 根据医嘱类型名称动态加载对应的医嘱名称字典数据
    await loadMedicalOrderNames(selectedOption.label)
  }

  // 清空医嘱名称选择，因为类型变化了
  addForm.orderItemName = ''

  // 如果是药品类医嘱，清空药品相关字段
  if (isDrugOrder(selectedOption.label)) {
    addForm.spec = ''
    addForm.frequencyCode = ''
    addForm.frequencyName = ''
    addForm.administrationRoute = ''
    addForm.dose = ''
    addForm.doseUnit = ''
  }
}

/**
 * 处理添加模式的医嘱名称选择变化
 */
const handleAddOrderNameChange = (value) => {
  console.log('添加模式 - 医嘱名称选择变化:', value)
  console.log('添加模式 - 当前医嘱类型:', addForm.orderClassName)
  console.log('添加模式 - 是否为药品类医嘱:', isDrugOrder(addForm.orderClassName))

  // 如果是药品类医嘱，从医嘱名称数据中提取药品信息
  if (isDrugOrder(addForm.orderClassName)) {
    const selectedOrderName = medicalOrderNameOptions.value.find(item => item.value === value)
    console.log('添加模式 - 选中的医嘱名称数据:', selectedOrderName)

    if (selectedOrderName) {
      // 从医嘱名称数据中提取药品信息
      extractDrugInfoFromOrderName(selectedOrderName, addForm)
    } else {
      console.warn('添加模式 - 未找到对应的医嘱名称数据')
    }
  }
}



/**
 * 处理编辑模式的备注字段失焦事件
 */
const handleRemarkBlur = () => {
  // 如果是药品类医嘱且有remark内容，尝试解析并自动填充
  if (isDrugOrder(editForm.orderClassName) && editForm.remark) {
    const parsedData = parseRemarkField(editForm.remark)
    if (parsedData.success) {
      autoFillDrugInfo(parsedData, editForm)
      ElMessage.success('已自动解析备注信息并填充到对应字段')
    } else if (editForm.remark.includes('##')) {
      ElMessage.warning('备注信息格式不完整，请检查是否按照"规格##频次##用法##用量##单位"的格式填写')
    }
  }
}

/**
 * 处理添加模式的备注字段失焦事件
 */
const handleAddRemarkBlur = () => {
  // 如果是药品类医嘱且有remark内容，尝试解析并自动填充
  if (isDrugOrder(addForm.orderClassName) && addForm.remark) {
    const parsedData = parseRemarkField(addForm.remark)
    if (parsedData.success) {
      autoFillDrugInfo(parsedData, addForm)
      ElMessage.success('已自动解析备注信息并填充到对应字段')
    } else if (addForm.remark.includes('##')) {
      ElMessage.warning('备注信息格式不完整，请检查是否按照"规格##频次##用法##用量##单位"的格式填写')
    }
  }
}

/**
 * 处理保存添加
 */
const handleSaveAdd = async () => {
  if (!props.patient?.visitSn) {
    ElMessage.error('缺少患者信息，无法保存')
    return
  }

  // 验证必填字段
  if (!addForm.orderType || !addForm.orderClassCode || !addForm.orderItemName) {
    ElMessage.error('请填写所有必填字段')
    return
  }

  // 构建保存数据（移到try块外面，这样catch块也能访问）
  const saveData = {
    visitSn: props.patient.visitSn,
    orderType: addForm.orderType,
    orderClassCode: addForm.orderClassCode,
    orderClassName: addForm.orderClassName,
    orderItemName: addForm.orderItemName,
    spec: addForm.spec || '',
    frequencyCode: addForm.frequencyCode || '',
    frequencyName: addForm.frequencyName || '',
    administrationRoute: addForm.administrationRoute || '',
    dose: addForm.dose || '',
    doseUnit: addForm.doseUnit || '',
    orderStartDatetime: addForm.orderStartDatetime || '',
    orderEndDatetime: addForm.orderEndDatetime || ''
  }

  try {
    saveLoading.value = true

    await apiServices.medicalOrder.save(saveData)

    ElMessage.success('添加成功')

    // 重新加载列表
    await loadMedicalOrderList()

    // 退出添加模式
    isAdding.value = false
    clearAddForm()

    // 触发数量更新事件
    emit('count-updated')
  } catch (error) {
    // 添加调试信息
    console.log('医嘱添加错误详情:', error)
    console.log('错误code:', error.code)
    console.log('错误message:', error.message)
    console.log('错误data:', error.data)

    // 检查是否是TNM分期诊断相关的错误（code为1002）
    if (error.code === 1002) {
      console.log('检测到code为1002，显示TNM分期诊断弹窗')
      // 保存当前数据，显示TNM分期诊断提示弹窗
      pendingSaveData.value = {
        saveData: saveData
      }
      pendingSaveType.value = 'add'
      showTnmDialog.value = true
    } else {
      ElMessage.error('添加失败: ' + error.message)
    }
  } finally {
    saveLoading.value = false
  }
}

// ========== TNM分期诊断弹窗处理方法 ==========

/**
 * 处理提交TNM诊断
 */
const handleSubmitTnmDiagnosis = () => {
  console.log('=== 用户点击提交TNM诊断 ===')
  console.log('当前患者信息:', props.patient)
  console.log('当前路由信息:', router.currentRoute.value)

  // 关闭弹窗
  showTnmDialog.value = false
  console.log('弹窗已关闭')

  // 跳转到TNM分期记录页面，并传递来源信息
  const currentParams = getAllUrlParams()
  console.log('当前URL参数:', currentParams)

  const routeParams = {
    name: 'PatientDetail',
    params: {
      visitSn: props.patient.visitSn
    },
    query: {
      ...currentParams,
      selectedMenuItem: 'tnm-staging',
      fromMedicalOrder: 'true' // 标记来源于医嘱页面
    }
  }

  console.log('准备跳转到TNM分期记录页面，路由参数:', routeParams)
  console.log('router对象:', router)

  // 检查是否已经在目标页面
  const currentRoute = router.currentRoute.value
  if (currentRoute.name === 'PatientDetail' &&
      currentRoute.params.visitSn === props.patient.visitSn) {
    console.log('在同一患者详情页面，使用replace更新URL参数')
    // 在同一页面内，使用replace更新URL参数来触发页面内容更新
    router.replace(routeParams).then(() => {
      console.log('页面参数更新成功')
    }).catch((error) => {
      console.error('页面参数更新失败:', error)
    })
  } else {
    console.log('跳转到不同页面，使用push')
    router.push(routeParams).then(() => {
      console.log('路由跳转成功')
    }).catch((error) => {
      console.error('路由跳转失败:', error)
    })
  }

  // 清空待保存数据
  pendingSaveData.value = null
  pendingSaveType.value = ''
  console.log('=== 提交TNM诊断处理完成 ===')
}

/**
 * 处理继续保存医嘱
 */
const handleContinueSaveOrder = async () => {
  if (!pendingSaveData.value) {
    ElMessage.error('没有待保存的数据')
    return
  }

  try {
    saveLoading.value = true

    if (pendingSaveType.value === 'edit') {
      // 编辑模式：强制保存
      await apiServices.medicalOrder.update(pendingSaveData.value.orderSn, pendingSaveData.value.saveData, { force: true })

      ElMessage.success('保存成功')

      // 重新加载列表
      await loadMedicalOrderList()

      // 退出编辑模式
      editingIndex.value = -1
      clearEditForm()

      // 触发数量更新事件
      emit('count-updated')
    } else if (pendingSaveType.value === 'add') {
      // 添加模式：强制保存
      await apiServices.medicalOrder.save(pendingSaveData.value.saveData, { force: true })

      ElMessage.success('添加成功')

      // 重新加载列表
      await loadMedicalOrderList()

      // 退出添加模式
      isAdding.value = false
      clearAddForm()

      // 触发数量更新事件
      emit('count-updated')
    }
  } catch (error) {
    ElMessage.error('保存失败: ' + error.message)
  } finally {
    saveLoading.value = false

    // 关闭弹窗并清空待保存数据
    showTnmDialog.value = false
    pendingSaveData.value = null
    pendingSaveType.value = ''
  }
}

/**
 * 处理取消TNM分期诊断弹窗
 */
const handleTnmDialogCancel = () => {
  // 关闭弹窗
  showTnmDialog.value = false

  // 清空待保存数据
  pendingSaveData.value = null
  pendingSaveType.value = ''
}

// ========== 过滤相关方法 ==========

/**
 * 处理搜索
 */
const handleSearch = () => {
  // 搜索功能通过计算属性 filteredMedicalOrderList 自动实现
  // 这里可以添加搜索成功的提示或其他逻辑
  if (filterOrderName.value) {
    const count = filteredMedicalOrderList.value.length
    // ElMessage.success(`找到 ${count} 条匹配的医嘱记录`)
  }
}

/**
 * 处理重置
 */
const handleReset = () => {
  filterOrderName.value = ''
  // ElMessage.success('过滤条件已重置')
}

// 监听患者变化，重新加载数据
watch(() => props.patient?.visitSn, (newVisitSn) => {
  if (newVisitSn) {
    loadMedicalOrderList()
  }
}, {immediate: false})

// 组件挂载时加载数据
onMounted(async () => {
  await loadDictionaries()
  if (props.patient?.visitSn) {
    await loadMedicalOrderList()
  }
})
</script>

<style scoped>
/* 整体容器 */
.medical-order-info-container {
  display: flex;
  width: 100%;
  height: calc(100vh - 180px);
  gap: 20px;
}

/* 右侧医嘱信息内容区域 */
.medical-order-content-area {
  flex: 1;
  height: 100%;
  background: #FFFFFF;
  border-radius: 0 12px 12px 0;
  padding: 16px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 加载容器 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 400px;
}

/* 内容包装器 */
.content-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

/* 页面标题 */
.page-title {
  margin-bottom: 20px;
  flex-shrink: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-text {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 18px;
  color: #172B4D;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin: 0;
}

/* 过滤区域样式 */
.filter-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-label {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 14px;
  color: #374151;
  white-space: nowrap;
  margin: 0;
}

.filter-select {
  width: 200px;
}

.filter-select :deep(.el-select__wrapper) {
  height: 32px;
  border-radius: 6px;
  border: 1px solid #D1D5DB;
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
}

.filter-buttons {
  display: flex;
  gap: 8px;
}

.filter-buttons .el-button {
  height: 32px;
  padding: 0 16px;
  font-size: 14px;
  border-radius: 6px;
}

/* 医嘱信息表格容器 */
.medical-order-table-container {
  flex: 1;
  overflow-x: auto;
  overflow-y: auto;
  width: 100%;
  position: relative;
}

/* 医嘱信息表格 */
.medical-order-table {
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;
  min-width: 1135px; /* 增加最小宽度以适应新增的备注列 */
  position: relative;
}

/* 表头样式 */
.table-header {
  height: 44px;
  background: #F4F5F7;
  border-radius: 0px 0px 0px 0px;
  border: 1px solid #EBECF0;
}

.header-cell {
  padding: 12px 8px;
  text-align: left;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 14px;
  color: #172B4D;
  line-height: 20px;
  border: 1px solid #EBECF0;
  white-space: nowrap;
}

/* 必填字段标识样式 */
.required-field {
  color: #FF4D4F;
  margin-right: 2px;
  font-weight: 500;
}
/* 医嘱类型 */
.header-cell:nth-child(1) {
  width: 80px;
}

/* 医嘱类别 */
.header-cell:nth-child(2) {
  width: 66px;
}

/* 医嘱名称 */
.header-cell:nth-child(3) {
  width: 160px;
}

/* 药品规格 */
.header-cell:nth-child(4) {
  width: 45px;
}

/* 频次 */
.header-cell:nth-child(5) {
  width: 40px;
}

/* 用法 */
.header-cell:nth-child(6) {
  width: 40px;
}

/* 用量 */
.header-cell:nth-child(7) {
  width: 40px;
}

/* 单位 */
.header-cell:nth-child(8) {
  width: 40px;
}

/* 开始时间 */
.header-cell:nth-child(9) {
  width: 80px;
}

/* 结束时间 */
.header-cell:nth-child(10) {
  width: 80px;
}

/* 操作 */
.header-cell:nth-child(11) {
  width: 80px;
  position: sticky;
  right: 0;
  background: #F4F5F7;
  z-index: 10;
}



/* 表格行样式 */
.table-row {
  height: 44px;
  background: #FFFFFF;
  border-radius: 0px 0px 0px 0px;
  border: 1px solid #EBECF0;
}

.table-row:hover {
  background: #F4F5F7;
}

.add-row {
  background: #FAFCFF !important;
  width: 100%;
}

.add-row:hover {
  background: #FAFCFF !important;
}

/* 表格单元格样式 */
.table-cell {
  padding: 6px 4px;
  text-align: left;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: #172B4D;
  line-height: 20px;
  border: 1px solid #EBECF0;
  vertical-align: middle;
  position: relative;
}

/* 操作列固定样式 */
.table-cell:last-child {
  position: sticky;
  right: 0;
  background: #FFFFFF;
  z-index: 9;
}

.table-row:hover .table-cell:last-child {
  background: #F4F5F7;
}

.add-row .table-cell:last-child {
  background: #FAFCFF !important;
}

/* 单元格内容不是输入框时的样式 */
.table-cell > span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
}

/* 输入框和选择框样式 */
.cell-input {
  width: 100%;
  height: 32px;
  border-radius: 0px;
  max-width: 100%;
}

.cell-input :deep(.el-input__wrapper),
.cell-input :deep(.el-select__wrapper) {
  height: 32px;
  border-radius: 0px;
  border: 1px solid #EBECF0;
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
  box-shadow: none;
  max-width: 100%;
}

/* 日期选择器特殊样式 */
.cell-input :deep(.el-date-editor) {
  width: 100% !important;
  max-width: 100% !important;
  --el-date-editor-width: 100% !important;
  --el-date-editor-daterange-width: 100% !important;
  --el-input-width: 100% !important;
}

.cell-input :deep(.el-date-editor .el-input__wrapper) {
  width: 100% !important;
  max-width: 100% !important;
}

/* 隐藏日期选择器的前缀图标 */

:deep(.el-icon) {
  display: none !important;
}

:deep(.el-input__icon) {
  display: none !important;
}

:deep(.el-input__prefix .el-icon) {
  display: none !important;
}

:deep(.el-input__prefix-inner .el-icon) {
  display: none !important;
}

:deep(.el-input__prefix svg) {
  display: none !important;
}

.cell-input :deep(.el-input__inner) {
  height: 30px;
  border: none;
  border-radius: 0px;
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
  color: #172B4D;
  box-shadow: none;
}

.cell-input :deep(.el-input__wrapper:focus),
.cell-input :deep(.el-select__wrapper.is-focused) {
  border-color: #EBECF0 !important;
  box-shadow: none !important;
}

.cell-input :deep(.el-input__inner:focus) {
  outline: none !important;
  border-color: #EBECF0 !important;
  box-shadow: none !important;
}

.cell-input :deep(.el-select__input:focus) {
  outline: none !important;
  border-color: #EBECF0 !important;
  box-shadow: none !important;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 4px;
  align-items: center;
  justify-content: center;
}

.action-buttons .el-button {
  height: 28px;
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 4px;
  min-width: auto;
}

/* 添加按钮容器样式 */
.add-button-container {
  position: sticky;
  left: 0;
  width: 100%;
  /* 移除 min-width 属性 */
  padding: 8px 12px;
  background: #FAFCFF;
  border-top: 1px solid #EBECF0;
  display: flex;
  justify-content: center;
  align-items: center;
  border-left: 1px solid #EBECF0;
  border-right: 1px solid #EBECF0;
  border-bottom: 1px solid #EBECF0;
  z-index: 8; /* 确保按钮在滚动时保持可见 */
}

/* 添加按钮样式 - 参考日常病程记录 */
.add-record-btn {
  width: 80px;
  height: 32px;
  background: transparent;
  border: none;
  border-radius: 4px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: #1678FF;
  line-height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  cursor: pointer;
}

.add-record-btn:hover {
  color: #1678FF;
  background: transparent;
  border: none;
}

.add-record-btn:focus {
  color: #1678FF;
  outline: none;
  background: transparent;
  border: none;
}

/* 新建模式时的激活状态 */
.add-record-btn.active {
  color: #1678FF;
}

/* ========== TNM分期诊断提示弹窗样式 ========== */

/* 弹窗遮罩层 */
.tnm-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
}

/* 弹窗容器 */
.tnm-dialog-container {
  background: #FFFFFF;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

/* 弹窗内容 */
.tnm-dialog-content {
  padding: 24px;
  background-color: rgba(255, 251, 230, 1);
  border-color: rgba(255, 229, 143, 1);
}

/* 弹窗标题 */
.tnm-dialog-title {
  margin-bottom: 5px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.tnm-dialog-title .note-icon {
  width: 24px;
  height: 26px;
  flex-shrink: 0;
  display: block;
}

.tnm-dialog-title .title-text {
  font-family: 'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-size: 18px;
  line-height: 20px; /* 调整行高与图标高度一致 */
  color: rgba(0, 0, 0, 0.647);
  margin: 0;
  font-weight: 500;
  display: flex;
  align-items: center;
}

/* 弹窗消息 */
.tnm-dialog-message {
  margin-bottom: 5px;
  margin-left: 32px; /* 图片宽度(20px) + 间距(8px) = 28px，与"请注意"文字对齐 */
}

.tnm-dialog-message .message-text {
  font-family: 'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-size: 14px;
  line-height: 26px;
  color: rgba(0, 0, 0, 0.447);
  margin: 0;
}

/* 弹窗操作按钮区域 */
.tnm-dialog-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
  margin-left: 32px; /* 图片宽度(20px) + 间距(8px) = 28px，与"请注意"文字对齐 */
}

/* 操作链接 */
.action-link {
  font-family: 'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-size: 14px;
  line-height: 26px;
  color: #1890FF;
  cursor: pointer;
  text-decoration: none;
  transition: color 0.2s ease;
}

.action-link:hover {
  color: #40a9ff;
  text-decoration: underline;
}

/* 返回链接特殊样式 */
.action-link.return-link {
  color: rgba(0, 178, 255, 0.647);
}

.action-link.return-link:hover {
  color: rgba(0, 178, 255, 0.8);
}

/* 分隔符 */
.action-separator {
  font-family: 'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-size: 14px;
  line-height: 26px;
  color: #CCCCCC;
  margin: 0 4px;
}

/* 加载更多指示器 */
.load-more-indicator {
  padding: 8px 12px;
  text-align: center;
  color: #999;
  font-size: 14px;
}

/* 加载更多触发器 */
.load-more-trigger {
  padding: 8px 12px;
  text-align: center;
  color: #1890ff;
  font-size: 14px;
  cursor: pointer;
  border-top: 1px solid #f0f0f0;
}

.load-more-trigger:hover {
  background-color: #f5f5f5;
  color: #40a9ff;
}
</style>
